"use client";

import React, { Component, ErrorInfo, ReactNode } from "react";
import { FiAlertTriangle, FiRefreshCw } from "react-icons/fi";

interface Props {
	children: ReactNode;
	fallback?: ReactNode;
}

interface State {
	hasError: boolean;
	error?: Error;
}

export class AuthErrorBoundary extends Component<Props, State> {
	constructor(props: Props) {
		super(props);
		this.state = { hasError: false };
	}

	static getDerivedStateFromError(error: Error): State {
		return { hasError: true, error };
	}

	componentDidCatch(error: Error, errorInfo: ErrorInfo) {
		console.error(
			"Authentication Error Boundary caught an error:",
			error,
			errorInfo
		);
	}

	handleRetry = () => {
		this.setState({ hasError: false, error: undefined });
		// Reload the page to reset authentication state
		window.location.reload();
	};

	render() {
		if (this.state.hasError) {
			if (this.props.fallback) {
				return this.props.fallback;
			}

			return (
				<div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-teal-100 via-teal-50 to-teal-200 py-12 px-4">
					<div className="bg-white shadow-2xl rounded-2xl p-8 text-center max-w-md w-full">
						<FiAlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-4" />
						<h1 className="text-2xl font-bold text-gray-900 mb-2">
							Authentication Error
						</h1>
						<p className="text-gray-600 mb-6">
							Something went wrong with the authentication system. Please try
							refreshing the page.
						</p>
						<button
							onClick={this.handleRetry}
							className="inline-flex items-center px-6 py-3 bg-teal-600 text-white font-medium rounded-lg hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 transition-all duration-200"
						>
							<FiRefreshCw className="mr-2 h-4 w-4" />
							Retry
						</button>
					</div>
				</div>
			);
		}

		return this.props.children;
	}
}

export default AuthErrorBoundary;
